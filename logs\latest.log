[01:23:38] [ServerMain/INFO]: [bootstrap] Running Java 24 (Java HotSpot(TM) 64-Bit Server VM 24.0.1+9-30; Oracle Corporation null) on Windows 11 10.0 (amd64)
[01:23:38] [ServerMain/INFO]: [bootstrap] Loading Paper 1.21.7-26-main@533d93c (2025-07-12T19:13:10Z) for Minecraft 1.21.7
[01:23:39] [ServerMain/INFO]: [PluginInitializerManager] Initializing plugins...
[01:23:39] [ServerMain/INFO]: [PluginInitializerManager] Initialized 1 plugin
[01:23:39] [ServerMain/INFO]: [PluginInitializerManager] Bukkit plugins (1):
 - Geyser-Spigot (2.8.2-SNAPSHOT)
[01:23:39] [Paper Plugin Remapper Thread - 0/INFO]: [ReobfServer] Remapping server...
[01:23:45] [Paper Plugin Remapper Thread - 0/INFO]: [ReobfServer] Done remapping server in 5118ms.
[01:23:45] [ServerMain/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[01:23:46] [ServerMain/INFO]: Found new data pack file/bukkit, loading it automatically
[01:23:46] [ServerMain/INFO]: Found new data pack paper, loading it automatically
[01:23:46] [ServerMain/INFO]: No existing world data, creating new world
[01:23:47] [ServerMain/INFO]: Loaded 1407 recipes
[01:23:47] [ServerMain/INFO]: Loaded 1520 advancements
[01:23:47] [ServerMain/INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Initialising converters for DataConverter...
[01:23:48] [ServerMain/INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Finished initialising converters for DataConverter in 406,3ms
[01:23:48] [Server thread/INFO]: Starting minecraft server version 1.21.7
[01:23:48] [Server thread/INFO]: Loading properties
[01:23:48] [Server thread/INFO]: This server is running Paper version 1.21.7-26-main@533d93c (2025-07-12T19:13:10Z) (Implementing API version 1.21.7-R0.1-SNAPSHOT)
[01:23:48] [Server thread/INFO]: [spark] This server bundles the spark profiler. For more information please visit https://docs.papermc.io/paper/profiling
[01:23:48] [Server thread/INFO]: Server Ping Player Sample Count: 12
[01:23:48] [Server thread/INFO]: Using 4 threads for Netty based IO
[01:23:48] [Server thread/INFO]: [MoonriseCommon] Paper is using 3 worker threads, 1 I/O threads
[01:23:48] [Server thread/INFO]: [ChunkTaskScheduler] Chunk system is using population gen parallelism: true
[01:23:49] [Server thread/INFO]: Default game type: SURVIVAL
[01:23:49] [Server thread/INFO]: Generating keypair
[01:23:49] [Server thread/INFO]: Starting Minecraft server on *:25565
[01:23:49] [Server thread/INFO]: Using default channel type
[01:23:49] [Server thread/INFO]: Paper: Using Java compression from Velocity.
[01:23:49] [Server thread/INFO]: Paper: Using Java cipher from Velocity.
[01:23:49] [Server thread/INFO]: [Geyser-Spigot] Loading server plugin Geyser-Spigot v2.8.2-SNAPSHOT
[01:23:50] [Server thread/INFO]: [Geyser-Spigot] Загрузка расширений...
[01:23:50] [Server thread/INFO]: [Geyser-Spigot] Загружено расширений: 0
[01:23:50] [Server thread/WARN]: **** SERVER IS RUNNING IN OFFLINE/INSECURE MODE!
[01:23:50] [Server thread/WARN]: The server will make no attempt to authenticate usernames. Beware.
[01:23:50] [Server thread/WARN]: While this makes the game possible to play without internet access, it also opens up the ability for hackers to connect with any username they choose.
[01:23:50] [Server thread/WARN]: To change this, set "online-mode" to "true" in the server.properties file.
[01:23:50] [Server thread/INFO]: Preparing level "world"
[01:23:54] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[01:23:54] [Server thread/INFO]: Preparing spawn area: 0%
[01:23:54] [Server thread/INFO]: Preparing spawn area: 2%
[01:23:55] [Server thread/INFO]: Preparing spawn area: 18%
[01:23:56] [Server thread/INFO]: Preparing spawn area: 28%
[01:23:56] [Server thread/INFO]: Preparing spawn area: 28%
[01:23:56] [Server thread/INFO]: Time elapsed: 2056 ms
[01:23:56] [Server thread/INFO]: Preparing start region for dimension minecraft:the_nether
[01:23:56] [Server thread/INFO]: Preparing spawn area: 2%
[01:23:57] [Server thread/INFO]: Preparing spawn area: 53%
[01:23:57] [Server thread/INFO]: Time elapsed: 599 ms
[01:23:57] [Server thread/INFO]: Preparing start region for dimension minecraft:the_end
[01:23:57] [Server thread/INFO]: Preparing spawn area: 2%
[01:23:57] [Server thread/INFO]: Time elapsed: 182 ms
[01:23:57] [Server thread/INFO]: [Geyser-Spigot] Enabling Geyser-Spigot v2.8.2-SNAPSHOT
[01:23:57] [Server thread/INFO]: [spark] Starting background profiler...
[01:23:57] [Server thread/INFO]: [spark] The async-profiler engine is not supported for your os/arch (windows11/amd64), so the built-in Java engine will be used instead.
[01:23:59] [Server thread/INFO]: [Geyser-Spigot] ******************************************
[01:23:59] [Server thread/INFO]: [Geyser-Spigot] 
[01:23:59] [Server thread/INFO]: [Geyser-Spigot] Загрузка Geyser версии 2.8.2-b881 (git-master-17bd368)
[01:23:59] [Server thread/INFO]: [Geyser-Spigot] 
[01:23:59] [Server thread/INFO]: [Geyser-Spigot] ******************************************
[01:24:02] [Server thread/INFO]: [Geyser-Spigot] Geyser запущен на UDP порту 19132
[01:24:02] [Server thread/INFO]: [Geyser-Spigot] Выполнено (3,178сек.)! Используйте команду /geyser help для помощи!
[01:24:02] [Server thread/INFO]: Done preparing level "world" (12.127s)
[01:24:02] [Server thread/INFO]: Running delayed init tasks
[01:24:02] [Server thread/INFO]: Done (25.143s)! For help, type "help"
[01:24:02] [Server thread/INFO]: *************************************************************************************
[01:24:02] [Server thread/INFO]: This is the first time you're starting this server.
[01:24:02] [Server thread/INFO]: It's recommended you read our 'Getting Started' documentation for guidance.
[01:24:02] [Server thread/INFO]: View this and more helpful information here: https://docs.papermc.io/paper/next-steps
[01:24:02] [Server thread/INFO]: *************************************************************************************
[01:24:05] [ForkJoinPool.commonPool-worker-11/INFO]: [Geyser-Spigot] Загрузка Minecraft JAR для извлечения необходимых файлов, пожалуйста, подождите... (это может занять некоторое время в зависимости от скорости подключения к Интернету)
[01:24:08] [Server thread/INFO]: ℹ Server Plugins (1):
[01:24:08] [Server thread/INFO]: Bukkit Plugins:
[01:24:08] [Server thread/INFO]:  - Geyser-Spigot
[01:24:31] [Server thread/INFO]: ---- Показана помощь по: Geyser (Страница 1/1) ----
[01:24:31] [Server thread/INFO]: /geyser connectiontest: Проверяет, доступен ли ваш сервер другим игрокам
[01:24:31] [Server thread/INFO]: /geyser dump: Собирает информацию отладки Geyser для баг-репортов.
[01:24:31] [Server thread/INFO]: /geyser help: Показывает помощь по всем зарегистрированным командам.
[01:24:31] [Server thread/INFO]: /geyser list: Список всех игроков, подключенных через Geyser.
[01:24:31] [Server thread/INFO]: /geyser reload: Перезагружает конфигурации Geyser. Отключает всех игроков при использовании!
[01:24:31] [Server thread/INFO]: /geyser version: Показывает текущую версию Geyser и проверяет наличие обновлений.
[01:24:45] [ForkJoinPool.commonPool-worker-11/INFO]: [Geyser-Spigot] JAR Minecraft был успешно скачан и загружен!
[01:24:45] [Server thread/INFO]: Unknown or incomplete command, see below for error
...ectiontest<--[HERE]
[01:24:47] [Server thread/INFO]: Unknown or incomplete command, see below for error
...ectiontest<--[HERE]
[01:24:58] [Server thread/INFO]: Unknown or incomplete command, see below for error
...ectiontest<--[HERE]
[01:25:02] [Server thread/INFO]: Игроки онлайн (0): 
[01:25:07] [Server thread/INFO]: На этом сервере Geyser версии 2.8.2-b881 (git-master-17bd368) (Java: 1.21.7, Bedrock: 1.21.70 - 1.21.73 - 1.21.93)
[01:25:07] [Server thread/INFO]: Проверка версии, пожалуйста подождите...
[01:25:07] [Server thread/INFO]: Обновлений нет.
[01:25:45] [Server thread/INFO]: Перезагрузка конфигураций Geyser... все подключенные клиенты bedrock будут отключены.
[01:25:45] [Geyser Scheduled Thread-2-1/INFO]: [Geyser-Spigot] Выключение Geyser.
[01:25:45] [nioEventLoopGroup-9-1/INFO]: [Geyser-Spigot] Geyser запущен на UDP порту 19132
