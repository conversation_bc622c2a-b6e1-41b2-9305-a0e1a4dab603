# --------------------------------
# Geyser Configuration File
#
# A bridge between Minecraft: Bedrock Edition and Minecraft: Java Edition.
#
# GitHub: https://github.com/GeyserMC/Geyser
# Discord: https://discord.gg/geysermc
# Wiki: https://wiki.geysermc.org/
#
# NOTICE: See https://wiki.geysermc.org/geyser/setup/ for the setup guide. Many video tutorials are outdated.
# In most cases, especially with server hosting providers, further hosting-specific configuration is required.
# --------------------------------

bedrock:
  # The IP address that will listen for connections.
  # Generally, you should only uncomment and change this if you want to limit what IPs can connect to your server.
  #address: 0.0.0.0
  # The port that will listen for connections
  port: 19132
  # Some hosting services change your Java port everytime you start the server and require the same port to be used for Bedrock.
  # This option makes the Bedrock port the same as the Java port every time you start the server.
  # This option is for the plugin version only.
  clone-remote-port: false
  # The MOTD that will be broadcasted to Minecraft: Bedrock Edition clients. This is irrelevant if "passthrough-motd" is set to true
  # If either of these are empty, the respective string will default to "Geyser"
  motd1: "Geyser"
  motd2: "Another Geyser server."
  # The Server Name that will be sent to Minecraft: Bedrock Edition clients. This is visible in both the pause menu and the settings menu.
  server-name: "Geyser"
  # How much to compress network traffic to the Bedrock client. The higher the number, the more CPU usage used, but
  # the smaller the bandwidth used. Does not have any effect below -1 or above 9. Set to -1 to disable.
  compression-level: 6
  # The port to broadcast to Bedrock clients with the MOTD that they should use to connect to the server.
  # DO NOT uncomment and change this unless Geyser runs on a different internal port than the one that is used to connect.
  # broadcast-port: 19132
  # Whether to enable PROXY protocol or not for clients. You DO NOT WANT this feature unless you run UDP reverse proxy
  # in front of your Geyser instance.
  enable-proxy-protocol: false
  # A list of allowed PROXY protocol speaking proxy IP addresses/subnets. Only effective when "enable-proxy-protocol" is enabled, and
  # should really only be used when you are not able to use a proper firewall (usually true with shared hosting providers etc.).
  # Keeping this list empty means there is no IP address whitelist.
  # IP addresses, subnets, and links to plain text files are supported.
  #proxy-protocol-whitelisted-ips: [ "127.0.0.1", "**********/16", "https://example.com/whitelist.txt" ]
remote:
  # The IP address of the remote (Java Edition) server
  # If it is "auto", for standalone version the remote address will be set to 127.0.0.1,
  # for plugin versions, it is recommended to keep this as "auto" so Geyser will automatically configure address, port, and auth-type.
  # Leave as "auto" if floodgate is installed.
  address: auto
  # The port of the remote (Java Edition) server
  # For plugin versions, if address has been set to "auto", the port will also follow the server's listening port.
  port: 25565
  # Authentication type. Can be offline, online, or floodgate (see https://github.com/GeyserMC/Geyser/wiki/Floodgate).
  # For plugin versions, it's recommended to keep the `address` field to "auto" so Floodgate support is automatically configured.
  # If Floodgate is installed and `address:` is set to "auto", then "auth-type: floodgate" will automatically be used.
  auth-type: online
  # Whether to enable PROXY protocol or not while connecting to the server.
  # This is useful only when:
  # 1) Your server supports PROXY protocol (it probably doesn't)
  # 2) You run Velocity or BungeeCord with the option enabled in the proxy's main config.
  # IF YOU DON'T KNOW WHAT THIS IS, DON'T TOUCH IT!
  use-proxy-protocol: false
  # Forward the hostname that the Bedrock client used to connect over to the Java server
  # This is designed to be used for forced hosts on proxies
  forward-hostname: false

# Floodgate uses encryption to ensure use from authorised sources.
# This should point to the public key generated by Floodgate (BungeeCord, Spigot or Velocity)
# You can ignore this when not using Floodgate.
# If you're using a plugin version of Floodgate on the same server, the key will automatically be picked up from Floodgate.
floodgate-key-file: key.pem

# For online mode authentication type only.
# Stores a list of Bedrock players that should have their Java Edition account saved after login.
# This saves a token that can be reused to authenticate the player later. This does not save emails or passwords,
# but you should still be cautious when adding to this list and giving others access to this Geyser instance's files.
# Removing a name from this list will delete its cached login information on the next Geyser startup.
# The file that tokens will be saved in is in the same folder as this config, named "saved-refresh-tokens.json".
saved-user-logins:
  - ThisExampleUsernameShouldBeLongEnoughToNeverBeAnXboxUsername
  - ThisOtherExampleUsernameShouldAlsoBeLongEnough

# Specify how many seconds to wait while user authorizes Geyser to access their Microsoft account.
# User is allowed to disconnect from the server during this period.
pending-authentication-timeout: 120

# Bedrock clients can freeze when opening up the command prompt for the first time if given a lot of commands.
# Disabling this will prevent command suggestions from being sent and solve freezing for Bedrock clients.
command-suggestions: true

# The following three options enable "ping passthrough" - the MOTD, player count and/or protocol name gets retrieved from the Java server.
# Relay the MOTD from the remote server to Bedrock players.
passthrough-motd: true
# Relay the player count and max players from the remote server to Bedrock players.
passthrough-player-counts: true
# Enable LEGACY ping passthrough. There is no need to enable this unless your MOTD or player count does not appear properly.
# This option does nothing on standalone.
legacy-ping-passthrough: false
# How often to ping the remote server, in seconds. Only relevant for standalone or legacy ping passthrough.
# Increase if you are getting BrokenPipe errors.
ping-passthrough-interval: 3

# Whether to forward player ping to the server. While enabling this will allow Bedrock players to have more accurate
# ping, it may also cause players to time out more easily.
forward-player-ping: false

# Maximum amount of players that can connect. This is only visual at this time and does not actually limit player count.
max-players: 100

# If debug messages should be sent through console
debug-mode: false

# Allow a fake cooldown indicator to be sent. Bedrock players otherwise do not see a cooldown as they still use 1.8 combat.
# Please note: if the cooldown is enabled, some users may see a black box during the cooldown sequence, like below:
# https://cdn.discordapp.com/attachments/613170125696270357/957075682230419466/Screenshot_from_2022-03-25_20-35-08.png
# This can be disabled by going into Bedrock settings under the accessibility tab and setting "Text Background Opacity" to 0
# This setting can be set to "title", "actionbar" or "false"
show-cooldown: title

# Controls if coordinates are shown to players.
show-coordinates: true

# Whether Bedrock players are blocked from performing their scaffolding-style bridging.
disable-bedrock-scaffolding: false

# If set, when a Bedrock player performs any emote, it will swap the offhand and mainhand items, just like the Java Edition keybind
# There are three options this can be set to:
# disabled - the default/fallback, which doesn't apply this workaround
# no-emotes - emotes will NOT be sent to other Bedrock clients and offhand will be swapped. This effectively disables all emotes from being seen.
# emotes-and-offhand - emotes will be sent to Bedrock clients and offhand will be swapped
emote-offhand-workaround: "disabled"

# The default locale if we dont have the one the client requested. Uncomment to not use the default system language.
# default-locale: en_us

# Specify how many days images will be cached to disk to save downloading them from the internet.
# A value of 0 is disabled. (Default: 0)
cache-images: 0

# Allows custom skulls to be displayed. Keeping them enabled may cause a performance decrease on older/weaker devices.
allow-custom-skulls: true

# The maximum number of custom skulls to be displayed per player. Increasing this may decrease performance on weaker devices.
# Setting this to -1 will cause all custom skulls to be displayed regardless of distance or number.
max-visible-custom-skulls: 128

# The radius in blocks around the player in which custom skulls are displayed.
custom-skull-render-distance: 32

# Whether to add any items and blocks which normally does not exist in Bedrock Edition.
# This should only need to be disabled if using a proxy that does not use the "transfer packet" style of server switching.
# If this is disabled, furnace minecart items will be mapped to hopper minecart items.
# Geyser's block, item, and skull mappings systems will also be disabled.
# This option requires a restart of Geyser in order to change its setting.
add-non-bedrock-items: true

# Bedrock prevents building and displaying blocks above Y127 in the Nether.
# This config option works around that by changing the Nether dimension ID to the End ID. 
# The main downside to this is that the entire Nether will have the same red fog rather than having different fog for each biome.
above-bedrock-nether-building: false

# Force clients to load all resource packs if there are any.
# If set to false, it allows the user to connect to the server even if they don't
# want to download the resource packs.
force-resource-packs: true

# Allows Xbox achievements to be unlocked.
xbox-achievements-enabled: false

# Whether player IP addresses will be logged by the server.
log-player-ip-addresses: true

# Whether to alert the console and operators that a new Geyser version is available that supports a Bedrock version
# that this Geyser version does not support. It's recommended to keep this option enabled, as many Bedrock platforms
# auto-update.
notify-on-new-bedrock-update: true

# Which item to use to mark unavailable slots in a Bedrock player inventory. Examples of this are the 2x2 crafting grid while in creative,
# or custom inventory menus with sizes different from the usual 3x9. A barrier block is the default item.
unusable-space-block: minecraft:barrier

# bStats is a stat tracker that is entirely anonymous and tracks only basic information
# about Geyser, such as how many people are online, how many servers are using Geyser,
# what OS is being used, etc. You can learn more about bStats here: https://bstats.org/.
# https://bstats.org/plugin/server-implementation/GeyserMC
metrics:
  # If metrics should be enabled
  enabled: true
  # UUID of server, don't change!
  uuid: 6293a6b1-2947-42f5-9cdf-88927f729983

# ADVANCED OPTIONS - DO NOT TOUCH UNLESS YOU KNOW WHAT YOU ARE DOING!

# Geyser updates the Scoreboard after every Scoreboard packet, but when Geyser tries to handle
# a lot of scoreboard packets per second can cause serious lag.
# This option allows you to specify after how many Scoreboard packets per seconds
# the Scoreboard updates will be limited to four updates per second.
scoreboard-packet-threshold: 20

# Allow connections from ProxyPass and Waterdog.
# See https://www.spigotmc.org/wiki/firewall-guide/ for assistance - use UDP instead of TCP.
enable-proxy-connections: false

# The internet supports a maximum MTU of 1492 but could cause issues with packet fragmentation.
# 1400 is the default.
mtu: 1400

# Whether to connect directly into the Java server without creating a TCP connection.
# This should only be disabled if a plugin that interfaces with packets or the network does not work correctly with Geyser.
# If enabled on plugin versions, the remote address and port sections are ignored
# If disabled on plugin versions, expect performance decrease and latency increase
use-direct-connection: true

# Whether Geyser should attempt to disable compression for Bedrock players. This should be a benefit as there is no need to compress data
# when Java packets aren't being handled over the network.
# This requires use-direct-connection to be true.
disable-compression: true

config-version: 4
