# --------------------------------
# Файл конфигурации Geyser
#
# Мост между Minecraft: Bedrock Edition и Minecraft: Java Edition.
#
# GitHub: https://github.com/GeyserMC/Geyser
# Discord: https://discord.gg/geysermc
# Wiki: https://wiki.geysermc.org/
#
# ВНИМАНИЕ: См. https://wiki.geysermc.org/geyser/setup/ для руководства по настройке. Многие видеоуроки устарели.
# В большинстве случаев, особенно с провайдерами хостинга серверов, требуется дополнительная конфигурация для хостинга.
# --------------------------------

bedrock:
  # IP-адрес, который будет прослушивать соединения.
  # Обычно вам следует раскомментировать и изменить это только если вы хотите ограничить, какие IP могут подключаться к вашему серверу.
  #address: 0.0.0.0
  # Порт, который будет прослушивать соединения
  port: 19132
  # Некоторые хостинг-сервисы изменяют ваш Java порт каждый раз при запуске сервера и требуют использования того же порта для Bedrock.
  # Эта опция делает порт Bedrock таким же, как порт Java каждый раз при запуске сервера.
  # Эта опция только для версии плагина.
  clone-remote-port: false
  # MOTD, который будет транслироваться клиентам Minecraft: Bedrock Edition. Это не имеет значения, если "passthrough-motd" установлено в true
  # Если любое из этих значений пустое, соответствующая строка по умолчанию будет "Geyser"
  motd1: "Geyser"
  motd2: "Another Geyser server."
  # Имя сервера, которое будет отправлено клиентам Minecraft: Bedrock Edition. Это видно как в меню паузы, так и в меню настроек.
  server-name: "Geyser"
  # Насколько сжимать сетевой трафик к клиенту Bedrock. Чем выше число, тем больше используется CPU, но
  # тем меньше используется пропускная способность. Не имеет эффекта ниже -1 или выше 9. Установите -1 для отключения.
  compression-level: 6
  # Порт для трансляции клиентам Bedrock с MOTD, который они должны использовать для подключения к серверу.
  # НЕ раскомментируйте и не изменяйте это, если только Geyser не работает на другом внутреннем порту, чем тот, который используется для подключения.
  # broadcast-port: 19132
  # Включать ли протокол PROXY для клиентов. Вам НЕ НУЖНА эта функция, если только вы не запускаете UDP обратный прокси
  # перед вашим экземпляром Geyser.
  enable-proxy-protocol: false
  # Список разрешенных IP-адресов/подсетей прокси, говорящих по протоколу PROXY. Действует только когда "enable-proxy-protocol" включен, и
  # должен использоваться только когда вы не можете использовать правильный брандмауэр (обычно это так с провайдерами общего хостинга и т.д.).
  # Оставление этого списка пустым означает отсутствие белого списка IP-адресов.
  # Поддерживаются IP-адреса, подсети и ссылки на текстовые файлы.
  #proxy-protocol-whitelisted-ips: [ "127.0.0.1", "**********/16", "https://example.com/whitelist.txt" ]
remote:
  # The IP address of the remote (Java Edition) server
  # If it is "auto", for standalone version the remote address will be set to 127.0.0.1,
  # for plugin versions, it is recommended to keep this as "auto" so Geyser will automatically configure address, port, and auth-type.
  # Leave as "auto" if floodgate is installed.
  address: auto
  # The port of the remote (Java Edition) server
  # For plugin versions, if address has been set to "auto", the port will also follow the server's listening port.
  port: 25565
  # Authentication type. Can be offline, online, or floodgate (see https://github.com/GeyserMC/Geyser/wiki/Floodgate).
  # For plugin versions, it's recommended to keep the `address` field to "auto" so Floodgate support is automatically configured.
  # If Floodgate is installed and `address:` is set to "auto", then "auth-type: floodgate" will automatically be used.
  auth-type: online
  # Whether to enable PROXY protocol or not while connecting to the server.
  # This is useful only when:
  # 1) Your server supports PROXY protocol (it probably doesn't)
  # 2) You run Velocity or BungeeCord with the option enabled in the proxy's main config.
  # IF YOU DON'T KNOW WHAT THIS IS, DON'T TOUCH IT!
  use-proxy-protocol: false
  # Forward the hostname that the Bedrock client used to connect over to the Java server
  # This is designed to be used for forced hosts on proxies
  forward-hostname: false

# Floodgate uses encryption to ensure use from authorised sources.
# This should point to the public key generated by Floodgate (BungeeCord, Spigot or Velocity)
# You can ignore this when not using Floodgate.
# If you're using a plugin version of Floodgate on the same server, the key will automatically be picked up from Floodgate.
floodgate-key-file: key.pem

# For online mode authentication type only.
# Stores a list of Bedrock players that should have their Java Edition account saved after login.
# This saves a token that can be reused to authenticate the player later. This does not save emails or passwords,
# but you should still be cautious when adding to this list and giving others access to this Geyser instance's files.
# Removing a name from this list will delete its cached login information on the next Geyser startup.
# The file that tokens will be saved in is in the same folder as this config, named "saved-refresh-tokens.json".
saved-user-logins:
  - ThisExampleUsernameShouldBeLongEnoughToNeverBeAnXboxUsername
  - ThisOtherExampleUsernameShouldAlsoBeLongEnough

# Specify how many seconds to wait while user authorizes Geyser to access their Microsoft account.
# User is allowed to disconnect from the server during this period.
pending-authentication-timeout: 120

# Клиенты Bedrock могут зависнуть при первом открытии командной строки, если им дано много команд.
# Отключение этого предотвратит отправку предложений команд и решит проблему зависания для клиентов Bedrock.
command-suggestions: true

# Следующие три опции включают "проброс пинга" - MOTD, количество игроков и/или имя протокола получается с Java сервера.
# Передавать MOTD с удаленного сервера игрокам Bedrock.
passthrough-motd: true
# Передавать количество игроков и максимальное количество игроков с удаленного сервера игрокам Bedrock.
passthrough-player-counts: true
# Включить УСТАРЕВШИЙ проброс пинга. Нет необходимости включать это, если ваш MOTD или количество игроков не отображается правильно.
# Эта опция ничего не делает в автономном режиме.
legacy-ping-passthrough: false
# Как часто пинговать удаленный сервер, в секундах. Актуально только для автономного или устаревшего проброса пинга.
# Увеличьте, если вы получаете ошибки BrokenPipe.
ping-passthrough-interval: 3

# Пересылать ли пинг игрока на сервер. Хотя включение этого позволит игрокам Bedrock иметь более точный
# пинг, это также может привести к тому, что игроки будут легче отключаться по таймауту.
forward-player-ping: false

# Максимальное количество игроков, которые могут подключиться. В настоящее время это только визуально и фактически не ограничивает количество игроков.
max-players: 100

# Должны ли отладочные сообщения отправляться через консоль
debug-mode: false

# Разрешить отправку поддельного индикатора перезарядки. Игроки Bedrock иначе не видят перезарядку, поскольку они все еще используют боевую систему 1.8.
# Обратите внимание: если перезарядка включена, некоторые пользователи могут видеть черный ящик во время последовательности перезарядки, как показано ниже:
# https://cdn.discordapp.com/attachments/613170125696270357/957075682230419466/Screenshot_from_2022-03-25_20-35-08.png
# Это можно отключить, зайдя в настройки Bedrock на вкладку доступности и установив "Непрозрачность фона текста" на 0
# Эта настройка может быть установлена на "title", "actionbar" или "false"
show-cooldown: title

# Контролирует, показываются ли координаты игрокам.
show-coordinates: true

# Блокируются ли игроки Bedrock от выполнения их строительства в стиле лесов.
disable-bedrock-scaffolding: false

# Если установлено, когда игрок Bedrock выполняет любой эмоут, это поменяет местами предметы в дополнительной и основной руке, как клавиша Java Edition
# Есть три варианта, на которые это может быть установлено:
# disabled - по умолчанию/резервный вариант, который не применяет это обходное решение
# no-emotes - эмоуты НЕ будут отправляться другим клиентам Bedrock, и дополнительная рука будет поменяна местами. Это эффективно отключает все эмоуты от просмотра.
# emotes-and-offhand - эмоуты будут отправляться клиентам Bedrock, и дополнительная рука будет поменяна местами
emote-offhand-workaround: "disabled"

# Локаль по умолчанию, если у нас нет той, которую запросил клиент. Раскомментируйте, чтобы не использовать язык системы по умолчанию.
# default-locale: en_us

# Укажите, сколько дней изображения будут кэшироваться на диск, чтобы сэкономить на их загрузке из интернета.
# Значение 0 отключено. (По умолчанию: 0)
cache-images: 0

# Позволяет отображать пользовательские черепа. Оставление их включенными может вызвать снижение производительности на старых/слабых устройствах.
allow-custom-skulls: true

# Максимальное количество пользовательских черепов для отображения на игрока. Увеличение этого может снизить производительность на слабых устройствах.
# Установка этого на -1 приведет к отображению всех пользовательских черепов независимо от расстояния или количества.
max-visible-custom-skulls: 128

# Радиус в блоках вокруг игрока, в котором отображаются пользовательские черепа.
custom-skull-render-distance: 32

# Добавлять ли любые предметы и блоки, которые обычно не существуют в Bedrock Edition.
# Это должно быть отключено только при использовании прокси, который не использует стиль переключения серверов "transfer packet".
# Если это отключено, предметы вагонетки с печью будут сопоставлены с предметами вагонетки с воронкой.
# Системы сопоставления блоков, предметов и черепов Geyser также будут отключены.
# Эта опция требует перезапуска Geyser для изменения настройки.
add-non-bedrock-items: true

# Bedrock предотвращает строительство и отображение блоков выше Y127 в Нижнем мире.
# Эта опция конфигурации обходит это, изменяя ID измерения Нижнего мира на ID Края.
# Основным недостатком этого является то, что весь Нижний мир будет иметь одинаковый красный туман, а не разный туман для каждого биома.
above-bedrock-nether-building: false

# Принуждать клиентов загружать все пакеты ресурсов, если они есть.
# Если установлено в false, это позволяет пользователю подключиться к серверу, даже если он не
# хочет загружать пакеты ресурсов.
force-resource-packs: true

# Позволяет разблокировать достижения Xbox.
xbox-achievements-enabled: false

# Будут ли IP-адреса игроков записываться сервером в логи.
log-player-ip-addresses: true

# Уведомлять ли консоль и операторов о том, что доступна новая версия Geyser, которая поддерживает версию Bedrock,
# которую эта версия Geyser не поддерживает. Рекомендуется оставить эту опцию включенной, поскольку многие платформы Bedrock
# обновляются автоматически.
notify-on-new-bedrock-update: true

# Какой предмет использовать для обозначения недоступных слотов в инвентаре игрока Bedrock. Примеры этого - сетка крафта 2x2 в творческом режиме,
# или пользовательские меню инвентаря с размерами, отличными от обычных 3x9. Блок барьера является предметом по умолчанию.
unusable-space-block: minecraft:barrier

# bStats - это трекер статистики, который полностью анонимен и отслеживает только базовую информацию
# о Geyser, такую как количество людей онлайн, количество серверов, использующих Geyser,
# какая ОС используется и т.д. Вы можете узнать больше о bStats здесь: https://bstats.org/.
# https://bstats.org/plugin/server-implementation/GeyserMC
metrics:
  # Должна ли быть включена метрика
  enabled: true
  # UUID сервера, не изменяйте!
  uuid: 6293a6b1-2947-42f5-9cdf-88927f729983

# РАСШИРЕННЫЕ ОПЦИИ - НЕ ТРОГАЙТЕ, ЕСЛИ НЕ ЗНАЕТЕ, ЧТО ДЕЛАЕТЕ!

# Geyser обновляет табло после каждого пакета табло, но когда Geyser пытается обработать
# много пакетов табло в секунду, это может вызвать серьезные лаги.
# Эта опция позволяет вам указать, после скольких пакетов табло в секунду
# обновления табло будут ограничены четырьмя обновлениями в секунду.
scoreboard-packet-threshold: 20

# Разрешить соединения от ProxyPass и Waterdog.
# См. https://www.spigotmc.org/wiki/firewall-guide/ для помощи - используйте UDP вместо TCP.
enable-proxy-connections: false

# Интернет поддерживает максимальный MTU 1492, но это может вызвать проблемы с фрагментацией пакетов.
# 1400 по умолчанию.
mtu: 1400

# Подключаться ли напрямую к Java серверу без создания TCP соединения.
# Это должно быть отключено только если плагин, который взаимодействует с пакетами или сетью, не работает корректно с Geyser.
# Если включено в версиях плагинов, секции удаленного адреса и порта игнорируются
# Если отключено в версиях плагинов, ожидайте снижения производительности и увеличения задержки
use-direct-connection: true

# Должен ли Geyser пытаться отключить сжатие для игроков Bedrock. Это должно быть преимуществом, поскольку нет необходимости сжимать данные,
# когда пакеты Java не обрабатываются по сети.
# Это требует, чтобы use-direct-connection было true.
disable-compression: true

config-version: 4
